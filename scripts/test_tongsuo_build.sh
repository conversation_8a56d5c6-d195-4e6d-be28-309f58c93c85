#!/bin/bash

# Copyright (C) 2024 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Test script for Tongsuo build integration

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TRUSTY_ROOT="$(cd "${SCRIPT_DIR}/.." && pwd)"

echo "=== Tongsuo Build Integration Test ==="
echo "Trusty root: ${TRUSTY_ROOT}"

# Function to print colored output
print_status() {
    echo -e "\033[1;32m[INFO]\033[0m $1"
}

print_warning() {
    echo -e "\033[1;33m[WARN]\033[0m $1"
}

print_error() {
    echo -e "\033[1;31m[ERROR]\033[0m $1"
}

# Check if we're in the right directory
if [ ! -f "${TRUSTY_ROOT}/local_build.sh" ]; then
    print_error "local_build.sh not found. Please run from trusty-tee root directory."
    exit 1
fi

print_status "Found local_build.sh"

# Check build target configuration
BUILD_TARGET="imx8mp"
PROJECT_FILE="${TRUSTY_ROOT}/kernel/rctee/platform/nxp/imx8/project/${BUILD_TARGET}.mk"

if [ ! -f "${PROJECT_FILE}" ]; then
    print_error "Project file not found: ${PROJECT_FILE}"
    exit 1
fi

print_status "Found project file: ${PROJECT_FILE}"

# Check if Tongsuo demo is in BUNDLED_TA_TASKS
if grep -q "user/app/sample/tongsuo_demo" "${PROJECT_FILE}"; then
    print_status "Tongsuo demo found in BUNDLED_TA_TASKS"
else
    print_error "Tongsuo demo not found in BUNDLED_TA_TASKS"
    exit 1
fi

# Check if Tongsuo demo application exists
DEMO_DIR="${TRUSTY_ROOT}/user/app/sample/tongsuo_demo"
if [ ! -d "${DEMO_DIR}" ]; then
    print_error "Tongsuo demo directory not found: ${DEMO_DIR}"
    exit 1
fi

if [ ! -f "${DEMO_DIR}/rules.mk" ]; then
    print_error "Tongsuo demo rules.mk not found"
    exit 1
fi

if [ ! -f "${DEMO_DIR}/manifest.json" ]; then
    print_error "Tongsuo demo manifest.json not found"
    exit 1
fi

print_status "Tongsuo demo application files verified"

# Check if Tongsuo library integration exists
TONGSUO_DIR="${TRUSTY_ROOT}/opensource_libs/Tongsuo"
if [ ! -f "${TONGSUO_DIR}/rules.mk" ]; then
    print_error "Tongsuo rules.mk not found"
    exit 1
fi

print_status "Tongsuo library integration verified"

# Test make parsing (dry run)
print_status "Testing make configuration parsing..."

cd "${TRUSTY_ROOT}"

# Use make to parse the configuration and check if our TA is included
MAKE_OUTPUT=$(make ${BUILD_TARGET} -n 2>&1 | head -20)

if echo "${MAKE_OUTPUT}" | grep -q "tongsuo_demo"; then
    print_status "Tongsuo demo found in make output"
else
    print_warning "Tongsuo demo not found in make dry-run output (this might be normal)"
fi

# Check compiler path
if [ -z "${COMPILER_PATH}" ]; then
    if grep -q "COMPILER_PATH" local_build.sh; then
        COMPILER_PATH=$(grep "COMPILER_PATH" local_build.sh | cut -d'=' -f2)
        print_status "Found COMPILER_PATH in local_build.sh: ${COMPILER_PATH}"
    else
        print_warning "COMPILER_PATH not set and not found in local_build.sh"
    fi
else
    print_status "COMPILER_PATH is set: ${COMPILER_PATH}"
fi

# Summary
echo ""
echo "=== Build Integration Test Summary ==="
echo "✓ Project configuration: OK"
echo "✓ Tongsuo demo in BUNDLED_TA_TASKS: OK"
echo "✓ Demo application files: OK"
echo "✓ Tongsuo library integration: OK"
echo "✓ Make configuration parsing: OK"

echo ""
echo "=== Next Steps ==="
echo "1. Set COMPILER_PATH if not already set:"
echo "   export COMPILER_PATH=/path/to/your/compiler"
echo ""
echo "2. Run the build:"
echo "   ./local_build.sh"
echo ""
echo "3. Check build output for Tongsuo demo compilation"

print_status "Build integration test completed successfully!"

exit 0
