# Copyright (C) 2024 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# This file is used only by Trusty for Tongsuo integration

LOCAL_DIR := $(GET_LOCAL_DIR)
LOCAL_PATH := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

TARGET_ARCH := $(ARCH)
TARGET_2ND_ARCH := $(ARCH)

# Reset local variables
LOCAL_CFLAGS :=
LOCAL_C_INCLUDES :=
LOCAL_SRC_FILES :=
LOCAL_SRC_FILES_$(TARGET_ARCH) :=
LOCAL_SRC_FILES_$(TARGET_2ND_ARCH) :=
LOCAL_CFLAGS_$(TARGET_ARCH) :=
LOCAL_CFLAGS_$(TARGET_2ND_ARCH) :=
LOCAL_ADDITIONAL_DEPENDENCIES :=

# Include Tongsuo crypto sources
MODULE_SRCDEPS += $(LOCAL_DIR)/crypto-sources.mk
include $(LOCAL_DIR)/crypto-sources.mk

# Tongsuo specific compilation flags
MODULE_CFLAGS += -D__linux__ -D__TRUSTY__

# Enable symbol prefix for coexistence with BoringSSL
# This allows Tongsuo and BoringSSL to coexist without symbol conflicts
MODULE_CFLAGS += -DTONGSUO_SYMBOL_PREFIX=TONGSUO_

# Tongsuo specific defines
MODULE_CFLAGS += -DOPENSSL_NO_ASM
MODULE_CFLAGS += -DOPENSSL_NO_THREADS_CORRUPT_MEMORY_AND_LEAK_SECRETS_IF_THREADED
MODULE_CFLAGS += -DOPENSSL_SMALL
MODULE_CFLAGS += -DOPENSSL_NO_DYNAMIC_ENGINE
MODULE_CFLAGS += -DOPENSSL_NO_STDIO
MODULE_CFLAGS += -DOPENSSL_NO_SOCK
MODULE_CFLAGS += -DOPENSSL_NO_DGRAM
MODULE_CFLAGS += -DOPENSSL_NO_UI_CONSOLE

# Disable features not needed in Trusty environment
MODULE_CFLAGS += -DOPENSSL_NO_APPS
MODULE_CFLAGS += -DOPENSSL_NO_TESTS
MODULE_CFLAGS += -DOPENSSL_NO_DEPRECATED

# Enable SM algorithms (Chinese national cryptographic algorithms)
MODULE_CFLAGS += -DOPENSSL_ENABLE_SM2
MODULE_CFLAGS += -DOPENSSL_ENABLE_SM3
MODULE_CFLAGS += -DOPENSSL_ENABLE_SM4

# Architecture specific optimizations
ifeq ($(ARCH),arm64)
MODULE_CFLAGS += -DOPENSSL_STATIC_ARMCAP
toarmcap = $(if $(filter-out 0 false,$(2)),-DOPENSSL_STATIC_ARMCAP_$(1),)
MODULE_CFLAGS += $(call toarmcap,NEON,$(USE_ARM_V7_NEON))
MODULE_CFLAGS += $(call toarmcap,AES,$(USE_ARM_V8_AES))
MODULE_CFLAGS += $(call toarmcap,PMULL,$(USE_ARM_V8_PMULL))
MODULE_CFLAGS += $(call toarmcap,SHA1,$(USE_ARM_V8_SHA1))
MODULE_CFLAGS += $(call toarmcap,SHA256,$(USE_ARM_V8_SHA2))
endif

# Add source files
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(LOCAL_SRC_FILES))
MODULE_SRCS += $(addprefix $(LOCAL_DIR)/,$(LOCAL_SRC_FILES_$(ARCH)))

# Include directories
MODULE_INCLUDES += $(LOCAL_DIR)/crypto
MODULE_INCLUDES += $(LOCAL_DIR)/include

# Export include directories for dependent modules
MODULE_EXPORT_INCLUDES += $(LOCAL_DIR)/include

# Include compatibility stubs if needed
include user/base/lib/openssl-stubs/openssl-stubs-inc.mk

include make/rctee_lib.mk
