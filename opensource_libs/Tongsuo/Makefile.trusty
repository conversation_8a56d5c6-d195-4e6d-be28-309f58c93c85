# Tongsuo Integration Makefile for Trusty TEE
# Copyright (C) 2024 The Android Open Source Project

.PHONY: help verify clean integration-test

# Default target
help:
	@echo "Tongsuo Integration for Trusty TEE"
	@echo ""
	@echo "Available targets:"
	@echo "  help           - Show this help message"
	@echo "  verify         - Verify integration setup"
	@echo "  integration-test - Run integration verification script"
	@echo "  clean          - Clean generated files"
	@echo "  check-symbols  - Check for symbol conflicts"
	@echo ""
	@echo "Usage examples:"
	@echo "  make verify              # Verify integration"
	@echo "  make integration-test    # Run full integration test"

# Verify integration setup
verify:
	@echo "Verifying Tongsuo integration..."
	@if [ ! -f "rules.mk" ]; then \
		echo "ERROR: rules.mk not found"; \
		exit 1; \
	fi
	@if [ ! -f "crypto-sources.mk" ]; then \
		echo "ERROR: crypto-sources.mk not found"; \
		exit 1; \
	fi
	@if [ ! -f "sources.mk" ]; then \
		echo "ERROR: sources.mk not found"; \
		exit 1; \
	fi
	@if [ ! -f "include/openssl/symbol_prefix.h" ]; then \
		echo "ERROR: symbol_prefix.h not found"; \
		exit 1; \
	fi
	@echo "✓ All integration files present"
	@if grep -q "TONGSUO_SYMBOL_PREFIX" rules.mk; then \
		echo "✓ Symbol prefix configuration found"; \
	else \
		echo "WARNING: Symbol prefix configuration not found"; \
	fi
	@echo "Integration verification complete"

# Run integration test script
integration-test:
	@echo "Running integration test..."
	@if [ -f "../../scripts/tongsuo_integration.sh" ]; then \
		cd ../.. && ./scripts/tongsuo_integration.sh; \
	else \
		echo "ERROR: Integration test script not found"; \
		exit 1; \
	fi

# Check for potential symbol conflicts
check-symbols:
	@echo "Checking for potential symbol conflicts..."
	@if [ -f "../boringssl/sources.mk" ]; then \
		echo "Comparing with BoringSSL symbols..."; \
		grep -h "^[[:space:]]*[a-zA-Z_][a-zA-Z0-9_]*\.c" sources.mk ../boringssl/sources.mk | \
		sort | uniq -d | head -10; \
	else \
		echo "BoringSSL sources.mk not found, skipping comparison"; \
	fi
	@echo "Symbol conflict check complete"

# Clean generated files
clean:
	@echo "Cleaning Tongsuo integration files..."
	@# Add cleanup commands here if needed
	@echo "Clean complete"

# Show integration status
status:
	@echo "Tongsuo Integration Status:"
	@echo "=========================="
	@echo "Integration files:"
	@ls -la rules.mk crypto-sources.mk sources.mk 2>/dev/null || echo "  Some files missing"
	@echo ""
	@echo "Symbol prefix header:"
	@ls -la include/openssl/symbol_prefix.h 2>/dev/null || echo "  symbol_prefix.h missing"
	@echo ""
	@echo "Demo application:"
	@ls -la ../../user/app/sample/tongsuo_demo/rules.mk 2>/dev/null || echo "  Demo app missing"
	@echo ""
	@echo "Configuration:"
	@grep -n "TONGSUO_SYMBOL_PREFIX" rules.mk 2>/dev/null || echo "  Symbol prefix not configured"
