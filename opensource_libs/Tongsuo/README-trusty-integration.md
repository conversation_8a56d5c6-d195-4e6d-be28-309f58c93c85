# Tongsuo Integration for Trusty TEE

本文档描述了如何在Trusty TEE中集成Tongsuo密码库，并实现与BoringSSL的无冲突共存。

## 概述

Tongsuo是一个提供现代密码学算法的开源密码库，特别支持中国国家密码算法（SM2、SM3、SM4）。本集成方案实现了：

1. **无冲突共存**：通过符号前缀技术，Tongsuo与BoringSSL可以在同一个二进制文件中共存
2. **自动集成**：无需手动创建外部链接，参考BoringSSL的集成方式
3. **命名空间隔离**：使用`TONGSUO_`前缀避免符号冲突
4. **兼容性维护**：保持与现有TA应用和构建系统的兼容性

## 集成架构

```
trusty-tee/
├── opensource_libs/
│   ├── boringssl/           # BoringSSL库（现有）
│   │   ├── rules.mk
│   │   └── crypto-sources.mk
│   └── Tongsuo/             # Tongsuo库（新增）
│       ├── rules.mk         # Trusty集成配置
│       ├── crypto-sources.mk # 源文件配置
│       ├── sources.mk       # 源文件列表
│       └── include/openssl/symbol_prefix.h # 符号前缀定义
└── user/app/sample/tongsuo_demo/ # 示例应用
```

## 核心特性

### 1. 符号前缀机制

所有Tongsuo的公共符号都使用`TONGSUO_`前缀：

```c
// BoringSSL函数（无前缀）
EVP_sha256()
RAND_bytes()

// Tongsuo函数（带前缀）
TONGSUO_EVP_sm3()
TONGSUO_SM2_encrypt()
```

### 2. 编译配置

在`rules.mk`中启用符号前缀：

```makefile
# 启用符号前缀以实现与BoringSSL共存
MODULE_CFLAGS += -DTONGSUO_SYMBOL_PREFIX=TONGSUO_

# 启用SM算法
MODULE_CFLAGS += -DOPENSSL_ENABLE_SM2
MODULE_CFLAGS += -DOPENSSL_ENABLE_SM3
MODULE_CFLAGS += -DOPENSSL_ENABLE_SM4
```

### 3. 依赖配置

在应用的`rules.mk`中同时依赖两个库：

```makefile
MODULE_LIBRARY_DEPS := \
    external/boringssl \
    external/Tongsuo \
    # 其他依赖...

# 启用Tongsuo符号前缀
MODULE_COMPILEFLAGS += -DTONGSUO_SYMBOL_PREFIX=TONGSUO_
```

## 使用方法

### 1. 基本使用

```c
#include <openssl/evp.h>        // BoringSSL头文件
#define TONGSUO_SYMBOL_PREFIX TONGSUO_
#include <openssl/symbol_prefix.h>  // Tongsuo符号前缀
#include <openssl/sm3.h>        // Tongsuo SM算法头文件

// 使用BoringSSL
EVP_MD_CTX *ctx = EVP_MD_CTX_new();
EVP_DigestInit_ex(ctx, EVP_sha256(), NULL);

// 使用Tongsuo（注意TONGSUO_前缀）
EVP_MD_CTX *sm3_ctx = TONGSUO_EVP_MD_CTX_new();
TONGSUO_EVP_DigestInit_ex(sm3_ctx, TONGSUO_EVP_sm3(), NULL);
```

### 2. SM算法示例

```c
// SM3哈希
unsigned char hash[32];
unsigned int hash_len;
TONGSUO_EVP_Digest(data, data_len, hash, &hash_len, TONGSUO_EVP_sm3(), NULL);

// SM4加密
EVP_CIPHER_CTX *cipher_ctx = TONGSUO_EVP_CIPHER_CTX_new();
TONGSUO_EVP_EncryptInit_ex(cipher_ctx, TONGSUO_EVP_sm4_cbc(), NULL, key, iv);

// SM2签名
EC_KEY *sm2_key = TONGSUO_SM2_key_new();
TONGSUO_SM2_generate_key(sm2_key);
TONGSUO_SM2_sign(data, data_len, signature, &sig_len, sm2_key);
```

## 构建说明

### 1. 验证集成

运行集成验证脚本：

```bash
./scripts/tongsuo_integration.sh
```

### 2. 编译项目

使用标准构建脚本：

```bash
./local_build.sh
```

### 3. 测试示例

编译并运行示例应用：

```bash
# 示例应用位于 user/app/sample/tongsuo_demo/
# 演示BoringSSL和Tongsuo的共存使用
```

## 技术实现

### 1. 符号隔离

通过预处理器宏实现符号重命名：

```c
#ifdef TONGSUO_SYMBOL_PREFIX
#define EVP_sm3 TONGSUO_EVP_sm3
#define SM2_encrypt TONGSUO_SM2_encrypt
// ... 更多符号定义
#endif
```

### 2. 编译时配置

- **无汇编优化**：`-DOPENSSL_NO_ASM`（适配Trusty环境）
- **小型化配置**：`-DOPENSSL_SMALL`（减少内存占用）
- **禁用不需要的功能**：`-DOPENSSL_NO_STDIO`等

### 3. 源文件管理

- `sources.mk`：定义所有需要编译的源文件
- `crypto-sources.mk`：配置编译标志和包含路径
- `rules.mk`：主要的Makefile集成文件

## 与BoringSSL对比

| 特性 | BoringSSL | Tongsuo |
|------|-----------|---------|
| 标准算法 | ✓ | ✓ |
| SM2公钥算法 | ✗ | ✓ |
| SM3哈希算法 | ✗ | ✓ |
| SM4对称算法 | ✗ | ✓ |
| 符号前缀 | 无 | TONGSUO_ |
| 内存占用 | 小 | 中等 |

## 注意事项

1. **符号前缀必须一致**：确保所有使用Tongsuo的模块都定义相同的符号前缀
2. **头文件顺序**：先包含BoringSSL头文件，再包含Tongsuo头文件
3. **链接顺序**：在`MODULE_LIBRARY_DEPS`中的顺序可能影响符号解析
4. **内存管理**：不要混用两个库的内存分配函数

## 故障排除

### 符号冲突错误

```
multiple definition of 'EVP_sha256'
```

**解决方案**：确保启用了符号前缀配置

### 编译错误

```
undefined reference to 'TONGSUO_EVP_sm3'
```

**解决方案**：检查是否正确包含了`symbol_prefix.h`

### 运行时错误

```
Segmentation fault
```

**解决方案**：检查是否混用了两个库的数据结构

## 参考资料

- [Tongsuo官方文档](https://www.tongsuo.net/docs/)
- [OpenSSL兼容性说明](https://www.tongsuo.net/docs/compilation/openssl-compatible)
- [BoringSSL集成参考](opensource_libs/boringssl/rules.mk)

## 版本信息

- Tongsuo版本：8.3.x+
- BoringSSL版本：兼容当前版本
- Trusty TEE：当前版本
- 符号前缀：TONGSUO_
