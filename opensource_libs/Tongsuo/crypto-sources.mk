# Now used only by Trusty for Tongsuo
LOCAL_ADDITIONAL_DEPENDENCIES += $(LOCAL_PATH)/sources.mk
include $(LOCAL_PATH)/sources.mk

# Tongsuo specific compilation flags for Trusty
LOCAL_CFLAGS += -I$(LOCAL_PATH)/include -I$(LOCAL_PATH)/crypto -Wno-unused-parameter
LOCAL_CFLAGS += -DTONGSUO_ANDROID_SYSTEM -DTONGSUO_SYMBOL_PREFIX=TONGSUO_

# Assembly flags
LOCAL_ASFLAGS += -I$(LOCAL_PATH)/include -I$(LOCAL_PATH)/crypto -Wno-unused-parameter

# Add crypto sources
LOCAL_SRC_FILES += $(crypto_sources) $(crypto_sources_asm)
