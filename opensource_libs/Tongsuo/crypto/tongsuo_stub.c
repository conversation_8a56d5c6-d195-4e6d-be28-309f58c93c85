/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

/*
 * Tongsuo stub implementation for Trusty TEE integration testing
 * This is a minimal placeholder implementation to verify the build system
 * integration. In a real deployment, this would be replaced with actual
 * Tongsuo source files.
 */

#include <stdio.h>

/* Stub function to verify Tongsuo library compilation */
int tongsuo_init(void) {
    printf("Tongsuo stub library initialized\n");
    return 0;
}

/* Stub function for SM3 hash */
int tongsuo_sm3_hash(const unsigned char *data, size_t len, unsigned char *hash) {
    printf("Tongsuo SM3 hash stub called\n");
    /* In real implementation, this would compute SM3 hash */
    return 0;
}

/* Stub function for SM4 encryption */
int tongsuo_sm4_encrypt(const unsigned char *in, unsigned char *out, 
                        const unsigned char *key) {
    printf("Tongsuo SM4 encrypt stub called\n");
    /* In real implementation, this would perform SM4 encryption */
    return 0;
}

/* Stub function for SM2 key generation */
int tongsuo_sm2_keygen(void **key) {
    printf("Tongsuo SM2 key generation stub called\n");
    /* In real implementation, this would generate SM2 key pair */
    return 0;
}

/* Version information */
const char *tongsuo_version(void) {
    return "Tongsuo-8.3.x-stub";
}

/* Library cleanup */
void tongsuo_cleanup(void) {
    printf("Tongsuo stub library cleanup\n");
}
