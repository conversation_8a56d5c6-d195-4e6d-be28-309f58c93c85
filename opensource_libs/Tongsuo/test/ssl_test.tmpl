[{-$testname-}]
ssl_conf = {-$testname-}-ssl

[{-$testname-}-ssl]
server = {-$testname-}-server
client = {-$testname-}-client{-
    # The following sections are optional.
    $OUT = "";
    if (%server2) {
        $OUT .= "\nserver2 = $testname-server2";
    } elsif ($reuse_server2) {
        $OUT .= "\nserver2 = $testname-server";
    }
    if (%resume_server) {
        $OUT .= "\nresume-server = $testname-resume-server";
    } elsif ($reuse_resume_server) {
        $OUT .= "\nresume-server = $testname-server";
    }
    if (%resume_client) {
        $OUT .= "\nresume-client = $testname-resume-client";
    } elsif ($reuse_resume_client) {
        $OUT .= "\nresume-client = $testname-client";
    }
-}

[{-$testname-}-server]
{-
    foreach my $key (sort keys %server) {
        # Emitted in the test section.
        next if ($key eq "extra");
        $OUT .= qq{$key} . " = " . qq{$server{$key}\n} if defined $server{$key};
    }
    if (%server2) {
        $OUT .= "\n[$testname-server2]\n";
        foreach my $key (sort keys %server2) {
            next if ($key eq "extra");
            $OUT .= qq{$key} . " = " . qq{$server2{$key}\n} if defined $server2{$key};
        }
    }
    if (%resume_server) {
        $OUT .= "\n[$testname-resume-server]\n";
        foreach my $key (sort keys %resume_server) {
            next if ($key eq "extra");
            $OUT .= qq{$key} . " = " . qq{$resume_server{$key}\n} if defined $resume_server{$key};
        }
    }
-}
[{-$testname-}-client]
{-
    foreach my $key (sort keys %client) {
        next if ($key eq "extra");
        $OUT .= qq{$key} . " = " . qq{$client{$key}\n} if defined $client{$key};
    }
    if (%resume_client) {
        $OUT .= "\n[$testname-resume-client]\n";
        foreach my $key (sort keys %resume_client) {
	    next if ($key eq "extra");
            $OUT .= qq{$key} . " = " . qq{$resume_client{$key}\n} if defined $resume_client{$key};
        }
    }
-}
[test-{-$idx-}]
{-
    foreach my $key (sort keys %test) {
        $OUT .= qq{$key} ." = " . qq{$test{$key}\n} if defined $test{$key};
    }

    # The extra server/client configuration sections.
    if ($server{"extra"}) {
       $OUT .= "server = $testname-server-extra\n";
    }
    if (%server2 && $server2{"extra"}) {
       $OUT .= "server2 = $testname-server2-extra\n";
    } elsif ($reuse_server2 && $server{"extra"}) {
       $OUT .= "server2 = $testname-server-extra\n";
    }
    if (%resume_server && $resume_server{"extra"}) {
       $OUT .= "resume-server = $testname-resume-server-extra\n";
    } elsif ($reuse_resume_server && $server{"extra"}) {
       $OUT .= "resume-server = $testname-server-extra\n";
    }
    if ($client{"extra"}) {
       $OUT .= "client = $testname-client-extra\n";
    }
    if (%resume_client && $resume_client{"extra"}) {
       $OUT .= "resume-client = $testname-resume-client-extra\n";
    } elsif ($reuse_resume_client && $client{"extra"}) {
       $OUT .= "resume-client = $testname-client-extra\n";
    }

    if ($server{"extra"}) {
        $OUT .= "\n[$testname-server-extra]\n";
        foreach my $key (sort keys %{$server{"extra"}}) {
            $OUT .= qq{$key} . " = " . qq{$server{"extra"}{$key}\n}
	    	 if defined $server{"extra"}{$key};
        }
    }
    if (%server2 && $server2{"extra"}) {
        $OUT .= "\n[$testname-server2-extra]\n";
        foreach my $key (sort keys %{$server2{"extra"}}) {
            $OUT .= qq{$key} . " = " . qq{$server2{"extra"}{$key}\n}
	    	 if defined $server2{"extra"}{$key};
        }
    }
   if (%resume_server && $resume_server{"extra"}) {
        $OUT .= "\n[$testname-resume-server-extra]\n";
        foreach my $key (sort keys %{$resume_server{"extra"}}) {
            $OUT .= qq{$key} . " = " . qq{$resume_server{"extra"}{$key}\n}
	    	 if defined $resume_server{"extra"}{$key};
        }
    }
   if ($client{"extra"}) {
        $OUT .= "\n[$testname-client-extra]\n";
        foreach my $key (sort keys %{$client{"extra"}}) {
            $OUT .= qq{$key} . " = " . qq{$client{"extra"}{$key}\n}
	    	 if defined $client{"extra"}{$key};
        }
   }
   if (%resume_client && $resume_client{"extra"}) {
        $OUT .= "\n[$testname-resume-client-extra]\n";
        foreach my $key (sort keys %{$resume_client{"extra"}}) {
            $OUT .= qq{$key} . " = " . qq{$resume_client{"extra"}{$key}\n}
	    	 if defined $resume_client{"extra"}{$key};
        }
    }
-}
