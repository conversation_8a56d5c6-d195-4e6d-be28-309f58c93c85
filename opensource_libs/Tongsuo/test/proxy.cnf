
## Config file for proxy certificate testing.

# Comment out the next line to ignore configuration errors
config_diagnostics = 1

[ req ]
distinguished_name	= req_distinguished_name_p1
encrypt_rsa_key		= no
default_md		= sha256

[ req_distinguished_name_p1 ]
countryName			= Country Name (2 letter code)
countryName_value		= AU
organizationName                = Organization Name (eg, company)
organizationName_value          = Dodgy Brothers
0.commonName			= Common Name (eg, YOUR name)
0.commonName_value		= Brother 1
1.commonName			= Common Name (eg, YOUR name)
1.commonName_value		= Brother 2
2.commonName			= Common Name (eg, YOUR name)
2.commonName_value		= Proxy 1

[ proxy ]
basicConstraints	= CA:FALSE
subjectKeyIdentifier	= hash
authorityKeyIdentifier	= keyid,issuer:always
proxyCertInfo	= critical,language:id-ppl-anyLanguage,pathlen:1,policy:text:AB

####################################################################

[ proxy2_req ]
distinguished_name	= req_distinguished_name_p2
encrypt_rsa_key		= no
default_md		= sha256

[ req_distinguished_name_p2 ]
countryName			= Country Name (2 letter code)
countryName_value		= AU
organizationName                = Organization Name (eg, company)
organizationName_value          = Dodgy Brothers
0.commonName			= Common Name (eg, YOUR name)
0.commonName_value		= Brother 1
1.commonName			= Common Name (eg, YOUR name)
1.commonName_value		= Brother 2
2.commonName			= Common Name (eg, YOUR name)
2.commonName_value		= Proxy 1
3.commonName			= Common Name (eg, YOUR name)
3.commonName_value		= Proxy 2

[ proxy_2 ]
basicConstraints	= CA:FALSE
subjectKeyIdentifier	= hash
authorityKeyIdentifier	= keyid,issuer:always
proxyCertInfo		= critical,@proxy_ext

[ proxy_ext ]
language	= id-ppl-anyLanguage
pathlen		= 0
policy		= text:BC
