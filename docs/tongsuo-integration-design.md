# Tongsuo与BoringSSL共存集成设计

## 1. 引言

### 1.1 背景
随着《密码法》的颁布实施，商密算法（SM2、SM3、SM4）和国密握手协议（TLCP）在金融、安全等领域的应用越来越重要。Trusty TEE需要支持这些国密算法，同时保持与现有BoringSSL的兼容性。

### 1.2 设计目标
- **无冲突共存**：Tongsuo与BoringSSL在同一二进制文件中无符号冲突
- **自动集成**：无需手动创建外部链接，参考BoringSSL集成方式
- **命名空间隔离**：通过符号前缀避免编译和运行时冲突
- **兼容性维护**：保持与现有TA应用和构建系统的兼容性

## 2. 总体设计

### 2.1 架构概览

```mermaid
graph TB
    subgraph "Trusty TEE Applications"
        A[TA Application 1]
        B[TA Application 2]
        C[Tongsuo Demo App]
    end
    
    subgraph "Crypto Libraries"
        D[BoringSSL<br/>标准密码算法]
        E[Tongsuo<br/>SM算法 + 标准算法<br/>符号前缀: TONGSUO_]
    end
    
    subgraph "Integration Layer"
        F[Symbol Prefix Headers]
        G[Build System Integration]
    end
    
    A --> D
    B --> D
    C --> D
    C --> E
    E --> F
    D --> G
    E --> G
```

### 2.2 核心技术方案

#### 2.2.1 符号前缀机制
- 所有Tongsuo公共符号添加`TONGSUO_`前缀
- 通过预处理器宏实现符号重命名
- 编译时隔离，避免链接冲突

#### 2.2.2 构建系统集成
- 参考BoringSSL的`rules.mk`模式
- 自动源文件管理和编译配置
- 无需手动外部链接创建

## 3. 详细设计

### 3.1 文件结构设计

```
opensource_libs/Tongsuo/
├── rules.mk                    # Trusty集成主配置文件
├── crypto-sources.mk           # 源文件和编译标志配置
├── sources.mk                  # 详细源文件列表
├── include/openssl/
│   └── symbol_prefix.h         # 符号前缀定义头文件
├── crypto/                     # Tongsuo源代码目录
├── ssl/                        # SSL/TLS实现
└── README-trusty-integration.md # 集成说明文档
```

### 3.2 符号前缀设计

#### 3.2.1 前缀规则
```c
// 原始符号 -> 前缀符号
EVP_sha256        -> TONGSUO_EVP_sha256
SM2_encrypt       -> TONGSUO_SM2_encrypt
RAND_bytes        -> TONGSUO_RAND_bytes
```

#### 3.2.2 实现机制
```c
#ifdef TONGSUO_SYMBOL_PREFIX
#define EVP_sm3 TONGSUO_EVP_sm3
#define SM2_encrypt TONGSUO_SM2_encrypt
#define SM4_encrypt TONGSUO_SM4_encrypt
// ... 更多符号定义
#endif
```

### 3.3 编译配置设计

#### 3.3.1 Tongsuo特定配置
```makefile
# 启用符号前缀
MODULE_CFLAGS += -DTONGSUO_SYMBOL_PREFIX=TONGSUO_

# 启用SM算法
MODULE_CFLAGS += -DOPENSSL_ENABLE_SM2
MODULE_CFLAGS += -DOPENSSL_ENABLE_SM3
MODULE_CFLAGS += -DOPENSSL_ENABLE_SM4

# Trusty环境适配
MODULE_CFLAGS += -DOPENSSL_NO_ASM
MODULE_CFLAGS += -DOPENSSL_SMALL
MODULE_CFLAGS += -DOPENSSL_NO_STDIO
```

#### 3.3.2 应用集成配置
```makefile
MODULE_LIBRARY_DEPS := \
    external/boringssl \
    external/Tongsuo \
    # 其他依赖...

MODULE_COMPILEFLAGS += -DTONGSUO_SYMBOL_PREFIX=TONGSUO_
```

### 3.4 使用接口设计

#### 3.4.1 头文件包含顺序
```c
// 1. 包含BoringSSL头文件（无前缀）
#include <openssl/evp.h>
#include <openssl/rand.h>

// 2. 定义Tongsuo符号前缀
#define TONGSUO_SYMBOL_PREFIX TONGSUO_

// 3. 包含Tongsuo符号前缀头文件
#include <openssl/symbol_prefix.h>

// 4. 包含Tongsuo特定头文件
#include <openssl/sm3.h>
#include <openssl/sm4.h>
#include <openssl/sm2.h>
```

#### 3.4.2 API使用示例
```c
// BoringSSL API（无前缀）
EVP_MD_CTX *ctx = EVP_MD_CTX_new();
EVP_DigestInit_ex(ctx, EVP_sha256(), NULL);

// Tongsuo API（带前缀）
EVP_MD_CTX *sm3_ctx = TONGSUO_EVP_MD_CTX_new();
TONGSUO_EVP_DigestInit_ex(sm3_ctx, TONGSUO_EVP_sm3(), NULL);
```

## 4. 系统错误处理设计

### 4.1 编译时错误处理

#### 4.1.1 符号冲突检测
- 构建系统检查重复符号定义
- 提供清晰的错误信息和解决建议

#### 4.1.2 配置验证
- 验证符号前缀配置的一致性
- 检查必要的编译标志

### 4.2 运行时错误处理

#### 4.2.1 库初始化
- 分别初始化BoringSSL和Tongsuo
- 错误状态隔离和报告

#### 4.2.2 内存管理
- 避免混用两个库的内存分配函数
- 提供内存泄漏检测机制

## 5. 系统维护设计

### 5.1 版本管理

#### 5.1.1 Tongsuo版本跟踪
- 记录集成的Tongsuo版本信息
- 提供版本兼容性检查

#### 5.1.2 BoringSSL兼容性
- 确保与现有BoringSSL版本兼容
- 提供升级路径和迁移指南

### 5.2 测试和验证

#### 5.2.1 集成测试
- 自动化集成验证脚本
- 符号冲突检测测试
- 功能正确性测试

#### 5.2.2 性能监控
- 内存使用情况监控
- 加密性能基准测试

## 6. 未解决问题

### 6.1 技术挑战

#### 6.1.1 汇编优化
- 当前禁用汇编优化以简化集成
- 未来可考虑架构特定的汇编优化

#### 6.1.2 动态库支持
- 当前仅支持静态链接
- 动态库共存需要额外的符号管理

### 6.2 功能限制

#### 6.2.1 SSL/TLS层
- 当前主要集成crypto层
- SSL/TLS层的完整集成需要进一步工作

#### 6.2.2 Provider机制
- OpenSSL 3.0的Provider机制集成
- 与Trusty TEE的适配

## 7. 总结

本设计实现了Tongsuo与BoringSSL的无冲突共存，通过符号前缀机制有效隔离了两个库的命名空间。集成方案参考了BoringSSL的成熟模式，确保了与现有构建系统的兼容性。

### 7.1 主要优势
- **无符号冲突**：通过前缀机制完全避免符号冲突
- **自动集成**：无需手动配置外部链接
- **功能完整**：支持SM2、SM3、SM4等国密算法
- **向后兼容**：不影响现有BoringSSL使用

### 7.2 应用场景
- 需要国密算法支持的金融应用
- 安全要求较高的政府应用
- 需要同时使用标准算法和国密算法的混合场景

通过这个集成方案，Trusty TEE能够同时提供国际标准密码算法和中国国家密码算法，满足不同应用场景的安全需求。
