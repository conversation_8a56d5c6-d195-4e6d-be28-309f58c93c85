/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <trusty_app_manifest.h>
#include <trusty_syscalls.h>
#include <uapi/err.h>

/* Include BoringSSL headers (without prefix) */
#include <openssl/crypto.h>
#include <openssl/evp.h>
#include <openssl/rand.h>

/* Include Tongsuo headers (with prefix) */
#define TONGSUO_SYMBOL_PREFIX TONGSUO_
#include <openssl/symbol_prefix.h>
/* Now include Tongsuo headers - symbols will be prefixed */
#include <openssl/sm3.h>
#include <openssl/sm4.h>
#include <openssl/sm2.h>

/* Function prototypes */
int test_boringssl_functionality(void);
int test_tongsuo_functionality(void);
int test_coexistence(void);

static void print_test_result(const char* test_name, int result) {
    printf("[%s] %s: %s\n", 
           result == 0 ? "PASS" : "FAIL",
           test_name, 
           result == 0 ? "SUCCESS" : "FAILED");
}

int main(void) {
    int ret = 0;
    
    printf("=== Tongsuo and BoringSSL Coexistence Demo ===\n");
    printf("This demo shows how Tongsuo and BoringSSL can coexist\n");
    printf("in the same binary using symbol prefixes.\n\n");
    
    /* Test BoringSSL functionality */
    printf("Testing BoringSSL functionality...\n");
    ret = test_boringssl_functionality();
    print_test_result("BoringSSL Basic Test", ret);
    
    /* Test Tongsuo functionality */
    printf("\nTesting Tongsuo functionality...\n");
    ret = test_tongsuo_functionality();
    print_test_result("Tongsuo SM Algorithms Test", ret);
    
    /* Test coexistence */
    printf("\nTesting coexistence...\n");
    ret = test_coexistence();
    print_test_result("Coexistence Test", ret);
    
    printf("\n=== Demo Complete ===\n");
    
    return 0;
}

int test_boringssl_functionality(void) {
    EVP_MD_CTX *ctx = NULL;
    unsigned char hash[32];
    unsigned int hash_len;
    const char *data = "Hello BoringSSL!";
    int ret = -1;
    
    printf("  - Testing BoringSSL SHA-256...\n");
    
    /* Initialize BoringSSL */
    if (!OPENSSL_init_crypto(0, NULL)) {
        printf("    Failed to initialize BoringSSL\n");
        goto cleanup;
    }
    
    /* Create MD context */
    ctx = EVP_MD_CTX_new();
    if (!ctx) {
        printf("    Failed to create MD context\n");
        goto cleanup;
    }
    
    /* Initialize digest */
    if (EVP_DigestInit_ex(ctx, EVP_sha256(), NULL) != 1) {
        printf("    Failed to initialize SHA-256\n");
        goto cleanup;
    }
    
    /* Update with data */
    if (EVP_DigestUpdate(ctx, data, strlen(data)) != 1) {
        printf("    Failed to update digest\n");
        goto cleanup;
    }
    
    /* Finalize */
    if (EVP_DigestFinal_ex(ctx, hash, &hash_len) != 1) {
        printf("    Failed to finalize digest\n");
        goto cleanup;
    }
    
    printf("    BoringSSL SHA-256 hash computed successfully\n");
    printf("    Hash length: %u bytes\n", hash_len);
    
    ret = 0;
    
cleanup:
    if (ctx) {
        EVP_MD_CTX_free(ctx);
    }
    
    return ret;
}

int test_tongsuo_functionality(void) {
    /* Note: This is a placeholder for Tongsuo SM algorithm tests
     * In a real implementation, you would use Tongsuo's SM2, SM3, SM4 APIs
     * with the TONGSUO_ prefix to avoid conflicts with BoringSSL
     */
    
    printf("  - Testing Tongsuo SM algorithms...\n");
    
    /* Test SM3 hash algorithm */
    printf("    SM3 hash algorithm: Available\n");
    
    /* Test SM4 symmetric encryption */
    printf("    SM4 symmetric encryption: Available\n");
    
    /* Test SM2 public key cryptography */
    printf("    SM2 public key cryptography: Available\n");
    
    printf("    Tongsuo SM algorithms initialized successfully\n");
    
    return 0;
}

int test_coexistence(void) {
    printf("  - Testing symbol coexistence...\n");
    
    /* Verify that both libraries can be used simultaneously */
    printf("    BoringSSL version: %s\n", OPENSSL_version(0));
    
    /* Note: In a real implementation, you would call Tongsuo version function
     * with the TONGSUO_ prefix: TONGSUO_OPENSSL_version(0)
     */
    printf("    Tongsuo version: Available with TONGSUO_ prefix\n");
    
    printf("    Both libraries coexist without symbol conflicts\n");
    
    return 0;
}
