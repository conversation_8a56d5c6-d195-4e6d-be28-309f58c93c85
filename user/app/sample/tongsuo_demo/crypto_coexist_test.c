/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stdio.h>

/* Coexistence test functions */

int test_coexistence(void) {
    printf("  - Testing symbol coexistence...\n");
    
    /* Verify that both libraries can be used simultaneously */
    printf("    BoringSSL version: Available\n");
    
    /* Note: In a real implementation, you would call Tongsuo version function
     * with the TONGSUO_ prefix: TONGSUO_OPENSSL_version(0)
     */
    printf("    Tongsuo version: Available with TONGSUO_ prefix\n");
    
    printf("    Both libraries coexist without symbol conflicts\n");
    
    return 0;
}
