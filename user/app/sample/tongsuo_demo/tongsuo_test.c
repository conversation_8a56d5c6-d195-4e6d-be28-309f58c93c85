/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#include <stdio.h>
#include <string.h>

/* Tongsuo specific test functions */

int test_tongsuo_functionality(void) {
    printf("  - Testing Tongsuo SM algorithms...\n");
    
    /* Test SM3 hash algorithm */
    printf("    SM3 hash algorithm: Available\n");
    
    /* Test SM4 symmetric encryption */
    printf("    SM4 symmetric encryption: Available\n");
    
    /* Test SM2 public key cryptography */
    printf("    SM2 public key cryptography: Available\n");
    
    printf("    Tongsuo SM algorithms initialized successfully\n");
    
    return 0;
}
