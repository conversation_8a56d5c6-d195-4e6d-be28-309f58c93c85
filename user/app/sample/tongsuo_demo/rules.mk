#
# Copyright (C) 2024 The Android Open Source Project
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#

LOCAL_DIR := $(GET_LOCAL_DIR)

MODULE := $(LOCAL_DIR)

MANIFEST := $(LOCAL_DIR)/manifest.json

MODULE_INCLUDES := $(LOCAL_DIR)/include

MODULE_SRCS := \
	$(LOCAL_DIR)/main.c \
	$(LOCAL_DIR)/tongsuo_test.c \
	$(LOCAL_DIR)/crypto_coexist_test.c \

# Demonstrate coexistence: depend on both BoringSSL and Tongsuo
MODULE_LIBRARY_DEPS := \
	opensource_libs/boringssl \
	opensource_libs/Tongsuo \
	user/base/lib/libc-rctee \
	user/base/lib/system_state \
	user/base/lib/tipc \

# Enable Tongsuo symbol prefix for coexistence
MODULE_COMPILEFLAGS += -DTONGSUO_SYMBOL_PREFIX=TONGSUO_

include make/ta.mk
